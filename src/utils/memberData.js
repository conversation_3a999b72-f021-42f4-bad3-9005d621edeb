/**
 * Member Data Utilities
 * 
 * Utilities for parsing and managing member data from CSV source.
 * This module handles the conversion of CSV data to structured member objects
 * and provides helper functions for member data manipulation.
 */

import { TypeCheckers, MemberStatus } from '../api/types.js';

/**
 * Raw member data from CSV (for demo purposes)
 * In production, this would come from an API endpoint
 */
const RAW_MEMBER_DATA = [
  {
    memberCode: 'MEM001',
    memberStatus: 'Active',
    titleTH: 'นาย',
    nameTH: 'สมชาย',
    surnameTH: 'ใจดี',
    titleEN: 'Mr.',
    nameEN: '<PERSON><PERSON><PERSON><PERSON>',
    surnameEN: '<PERSON><PERSON><PERSON>',
    citizenID: '1234567890123',
    otherID: 'EMP001',
    staffNo: 'ST001',
    insurerCardNo: 'IC001',
    insPreviousCardNo: 'IPC001',
    policyNo: 'POL001',
    certificateNo: 'CERT001',
    memberType: 'Principal',
    principleMemberCode: 'MEM001',
    principleName: 'สมชาย ใจดี',
    vip: 'N',
    vipRemarks: '',
    cardType: 'Standard',
    language: 'TH',
    insurerCode: 'INS001',
    insurerName: 'บริษัท ประกันภัย เอ จำกัด',
    insurerNameEN: 'Insurance Company A Ltd.',
    companyCode: 'COM001',
    companyName: 'บริษัท ABC จำกัด',
    companyNameEN: 'ABC Company Ltd.',
    birthDate: '1985-03-15',
    gender: 'M',
    citizenship: 'Thai',
    countryCode: 'TH',
    planCode: 'PLAN001',
    planName: 'แผนประกันสุขภาพพื้นฐาน',
    planEffFrom: '2025-01-01',
    planEffTo: '2025-12-31',
    mobile: '0812345678',
    email: '<EMAIL>'
  },
  {
    memberCode: 'MEM002',
    memberStatus: 'Active',
    titleTH: 'นางสาว',
    nameTH: 'สุดา',
    surnameTH: 'รักดี',
    titleEN: 'Ms.',
    nameEN: 'Suda',
    surnameEN: 'Rakdee',
    citizenID: '1234567890124',
    otherID: 'EMP002',
    staffNo: 'ST002',
    insurerCardNo: 'IC002',
    insPreviousCardNo: 'IPC002',
    policyNo: 'POL001',
    certificateNo: 'CERT002',
    memberType: 'Dependent',
    principleMemberCode: 'MEM001',
    principleName: 'สมชาย ใจดี',
    vip: 'N',
    vipRemarks: '',
    cardType: 'Standard',
    language: 'TH',
    insurerCode: 'INS001',
    insurerName: 'บริษัท ประกันภัย เอ จำกัด',
    insurerNameEN: 'Insurance Company A Ltd.',
    companyCode: 'COM001',
    companyName: 'บริษัท ABC จำกัด',
    companyNameEN: 'ABC Company Ltd.',
    birthDate: '1987-07-22',
    gender: 'F',
    citizenship: 'Thai',
    countryCode: 'TH',
    planCode: 'PLAN001',
    planName: 'แผนประกันสุขภาพพื้นฐาน',
    planEffFrom: '2025-01-01',
    planEffTo: '2025-12-31',
    mobile: '0823456789',
    email: '<EMAIL>'
  },
  {
    memberCode: 'MEM003',
    memberStatus: 'Active',
    titleTH: 'นาง',
    nameTH: 'มาลี',
    surnameTH: 'สวยงาม',
    titleEN: 'Mrs.',
    nameEN: 'Malee',
    surnameEN: 'Suayngam',
    citizenID: '1234567890125',
    otherID: 'EMP003',
    staffNo: 'ST003',
    insurerCardNo: 'IC003',
    insPreviousCardNo: 'IPC003',
    policyNo: 'POL002',
    certificateNo: 'CERT003',
    memberType: 'Principal',
    principleMemberCode: 'MEM003',
    principleName: 'มาลี สวยงาม',
    vip: 'Y',
    vipRemarks: 'VIP Customer',
    cardType: 'Gold',
    language: 'TH',
    insurerCode: 'INS001',
    insurerName: 'บริษัท ประกันภัย เอ จำกัด',
    insurerNameEN: 'Insurance Company A Ltd.',
    companyCode: 'COM002',
    companyName: 'บริษัท XYZ จำกัด',
    companyNameEN: 'XYZ Company Ltd.',
    birthDate: '1980-12-10',
    gender: 'F',
    citizenship: 'Thai',
    countryCode: 'TH',
    planCode: 'PLAN002',
    planName: 'แผนประกันสุขภาพพรีเมี่ยม',
    planEffFrom: '2025-01-01',
    planEffTo: '2025-12-31',
    mobile: '0834567890',
    email: '<EMAIL>'
  },
  {
    memberCode: 'MEM004',
    memberStatus: 'Active',
    titleTH: 'นาย',
    nameTH: 'วิชัย',
    surnameTH: 'เก่งมาก',
    titleEN: 'Mr.',
    nameEN: 'Wichai',
    surnameEN: 'Kengmak',
    citizenID: '1234567890126',
    otherID: 'EMP004',
    staffNo: 'ST004',
    insurerCardNo: 'IC004',
    insPreviousCardNo: 'IPC004',
    policyNo: 'POL003',
    certificateNo: 'CERT004',
    memberType: 'Principal',
    principleMemberCode: 'MEM004',
    principleName: 'วิชัย เก่งมาก',
    vip: 'N',
    vipRemarks: '',
    cardType: 'Standard',
    language: 'EN',
    insurerCode: 'INS002',
    insurerName: 'Health Insurance Co. Ltd.',
    insurerNameEN: 'Health Insurance Co. Ltd.',
    companyCode: 'COM003',
    companyName: 'DEF Corporation',
    companyNameEN: 'DEF Corporation',
    birthDate: '1975-05-18',
    gender: 'M',
    citizenship: 'Thai',
    countryCode: 'TH',
    planCode: 'PLAN003',
    planName: 'Executive Health Plan',
    planEffFrom: '2025-01-01',
    planEffTo: '2025-12-31',
    mobile: '**********',
    email: '<EMAIL>'
  },
  {
    memberCode: 'MEM005',
    memberStatus: 'Active',
    titleTH: 'นางสาว',
    nameTH: 'นิดา',
    surnameTH: 'ขยันดี',
    titleEN: 'Ms.',
    nameEN: 'Nida',
    surnameEN: 'Khayanee',
    citizenID: '1234567890127',
    otherID: 'EMP005',
    staffNo: 'ST005',
    insurerCardNo: 'IC005',
    insPreviousCardNo: 'IPC005',
    policyNo: 'POL003',
    certificateNo: 'CERT005',
    memberType: 'Dependent',
    principleMemberCode: 'MEM004',
    principleName: 'วิชัย เก่งมาก',
    vip: 'N',
    vipRemarks: '',
    cardType: 'Standard',
    language: 'EN',
    insurerCode: 'INS002',
    insurerName: 'Health Insurance Co. Ltd.',
    insurerNameEN: 'Health Insurance Co. Ltd.',
    companyCode: 'COM003',
    companyName: 'DEF Corporation',
    companyNameEN: 'DEF Corporation',
    birthDate: '1990-09-25',
    gender: 'F',
    citizenship: 'Thai',
    countryCode: 'TH',
    planCode: 'PLAN003',
    planName: 'Executive Health Plan',
    planEffFrom: '2025-01-01',
    planEffTo: '2025-12-31',
    mobile: '**********',
    email: '<EMAIL>'
  },
  {
    memberCode: 'MEM006',
    memberStatus: 'Active',
    titleTH: 'นาย',
    nameTH: 'ประยุทธ',
    surnameTH: 'มั่นคง',
    titleEN: 'Mr.',
    nameEN: 'Prayuth',
    surnameEN: 'Mankong',
    citizenID: '1234567890128',
    otherID: 'EMP006',
    staffNo: 'ST006',
    insurerCardNo: 'IC006',
    insPreviousCardNo: 'IPC006',
    policyNo: 'POL004',
    certificateNo: 'CERT006',
    memberType: 'Principal',
    principleMemberCode: 'MEM006',
    principleName: 'ประยุทธ มั่นคง',
    vip: 'Y',
    vipRemarks: 'Executive VIP',
    cardType: 'Platinum',
    language: 'TH',
    insurerCode: 'INS003',
    insurerName: 'บริษัท ประกันชีวิต ซี จำกัด',
    insurerNameEN: 'Life Insurance C Ltd.',
    companyCode: 'COM004',
    companyName: 'บริษัท GHI จำกัด',
    companyNameEN: 'GHI Company Ltd.',
    birthDate: '1970-11-30',
    gender: 'M',
    citizenship: 'Thai',
    countryCode: 'TH',
    planCode: 'PLAN004',
    planName: 'แผนประกันชีวิตและสุขภาพ',
    planEffFrom: '2025-01-01',
    planEffTo: '2025-12-31',
    mobile: '0867890123',
    email: '<EMAIL>'
  },
  // Add remaining members (MEM007-MEM012) with abbreviated data for brevity
  {
    memberCode: 'MEM007',
    memberStatus: 'Active',
    titleTH: 'นาง',
    nameTH: 'สมหญิง',
    surnameTH: 'ดีใจ',
    titleEN: 'Mrs.',
    nameEN: 'Somying',
    surnameEN: 'Deejai',
    citizenID: '1234567890129',
    memberType: 'Principal',
    principleMemberCode: 'MEM007',
    principleName: 'สมหญิง ดีใจ',
    vip: 'N',
    cardType: 'Standard',
    language: 'TH',
    mobile: '0878901234',
    email: '<EMAIL>'
  },
  {
    memberCode: 'MEM008',
    memberStatus: 'Active',
    titleTH: 'นาย',
    nameTH: 'อนุชา',
    surnameTH: 'รวยมาก',
    titleEN: 'Mr.',
    nameEN: 'Anucha',
    surnameEN: 'Ruaimak',
    citizenID: '1234567890130',
    memberType: 'Principal',
    principleMemberCode: 'MEM008',
    principleName: 'อนุชา รวยมาก',
    vip: 'Y',
    vipRemarks: 'High Net Worth',
    cardType: 'Diamond',
    language: 'EN',
    mobile: '0889012345',
    email: '<EMAIL>'
  },
  {
    memberCode: 'MEM009',
    memberStatus: 'Active',
    titleTH: 'นางสาว',
    nameTH: 'ปิยะดา',
    surnameTH: 'สุขใส',
    titleEN: 'Ms.',
    nameEN: 'Piyada',
    surnameEN: 'Suksai',
    citizenID: '1234567890131',
    memberType: 'Principal',
    principleMemberCode: 'MEM009',
    principleName: 'ปิยะดา สุขใส',
    vip: 'N',
    cardType: 'Standard',
    language: 'TH',
    mobile: '0890123456',
    email: '<EMAIL>'
  },
  {
    memberCode: 'MEM010',
    memberStatus: 'Active',
    titleTH: 'นาย',
    nameTH: 'ธนากร',
    surnameTH: 'เจริญรุ่ง',
    titleEN: 'Mr.',
    nameEN: 'Thanakorn',
    surnameEN: 'Charoenrung',
    citizenID: '1234567890132',
    memberType: 'Principal',
    principleMemberCode: 'MEM010',
    principleName: 'ธนากร เจริญรุ่ง',
    vip: 'N',
    cardType: 'Standard',
    language: 'EN',
    mobile: '0801234567',
    email: '<EMAIL>'
  },
  {
    memberCode: 'MEM011',
    memberStatus: 'Inactive',
    titleTH: 'นาย',
    nameTH: 'สมศักดิ์',
    surnameTH: 'หมดอายุ',
    titleEN: 'Mr.',
    nameEN: 'Somsak',
    surnameEN: 'Modayu',
    citizenID: '1234567890133',
    memberType: 'Principal',
    principleMemberCode: 'MEM011',
    principleName: 'สมศักดิ์ หมดอายุ',
    vip: 'N',
    cardType: 'Standard',
    language: 'TH',
    mobile: '0812345679',
    email: '<EMAIL>'
  },
  {
    memberCode: 'MEM012',
    memberStatus: 'Active',
    titleTH: 'นางสาว',
    nameTH: 'วรรณา',
    surnameTH: 'ใหม่สด',
    titleEN: 'Ms.',
    nameEN: 'Wanna',
    surnameEN: 'Maisod',
    citizenID: '1234567890134',
    memberType: 'Principal',
    principleMemberCode: 'MEM012',
    principleName: 'วรรณา ใหม่สด',
    vip: 'N',
    cardType: 'Standard',
    language: 'TH',
    mobile: '0823456780',
    email: '<EMAIL>'
  }
];

/**
 * Get all available members
 * @param {boolean} includeInactive - Whether to include inactive members
 * @returns {Array} Array of member objects
 */
export function getAllMembers(includeInactive = false) {
  if (includeInactive) {
    return [...RAW_MEMBER_DATA];
  }
  return RAW_MEMBER_DATA.filter(member => member.memberStatus === MemberStatus.ACTIVE);
}

/**
 * Get member by member code
 * @param {string} memberCode - Member code to search for
 * @returns {Object|null} Member object or null if not found
 */
export function getMemberByCode(memberCode) {
  return RAW_MEMBER_DATA.find(member => member.memberCode === memberCode) || null;
}

/**
 * Get member display name based on language preference
 * @param {Object} member - Member object
 * @param {string} language - Language preference ('TH' or 'EN')
 * @returns {string} Formatted display name
 */
export function getMemberDisplayName(member, language = 'EN') {
  if (!member) return '';

  if (language === 'TH' && member.titleTH && member.nameTH && member.surnameTH) {
    return `${member.titleTH}${member.nameTH} ${member.surnameTH}`;
  }

  if (member.titleEN && member.nameEN && member.surnameEN) {
    return `${member.titleEN} ${member.nameEN} ${member.surnameEN}`;
  }

  // Fallback to member code if names are not available
  return member.memberCode || 'Unknown Member';
}

/**
 * Get member short name (first name only)
 * @param {Object} member - Member object
 * @param {string} language - Language preference ('TH' or 'EN')
 * @returns {string} Short name
 */
export function getMemberShortName(member, language = 'EN') {
  if (!member) return '';

  if (language === 'TH' && member.nameTH) {
    return member.nameTH;
  }

  if (member.nameEN) {
    return member.nameEN;
  }

  return member.memberCode || 'Unknown';
}

/**
 * Get members for dropdown display
 * @param {boolean} includeInactive - Whether to include inactive members
 * @returns {Array} Array of member options for dropdown
 */
export function getMemberOptions(includeInactive = false) {
  const members = getAllMembers(includeInactive);

  return members.map(member => ({
    value: member.memberCode,
    label: getMemberDisplayName(member, member.language || 'EN'),
    shortLabel: getMemberShortName(member, member.language || 'EN'),
    status: member.memberStatus,
    vip: member.vip === 'Y',
    cardType: member.cardType,
    memberType: member.memberType,
    language: member.language,
    member: member // Include full member object for reference
  }));
}

/**
 * Filter members by search term
 * @param {string} searchTerm - Search term to filter by
 * @param {boolean} includeInactive - Whether to include inactive members
 * @returns {Array} Filtered array of members
 */
export function searchMembers(searchTerm, includeInactive = false) {
  if (!searchTerm || searchTerm.trim() === '') {
    return getAllMembers(includeInactive);
  }

  const term = searchTerm.toLowerCase().trim();
  const members = getAllMembers(includeInactive);

  return members.filter(member => {
    const searchFields = [
      member.memberCode,
      member.nameTH,
      member.surnameTH,
      member.nameEN,
      member.surnameEN,
      member.citizenID,
      member.email,
      member.mobile
    ];

    return searchFields.some(field =>
      field && field.toLowerCase().includes(term)
    );
  });
}

/**
 * Validate member data structure
 * @param {Object} member - Member object to validate
 * @returns {Object} Validation result
 */
export function validateMemberData(member) {
  return TypeCheckers.validateMember(member);
}

/**
 * Get default member (first active member)
 * @returns {Object|null} Default member object
 */
export function getDefaultMember() {
  const activeMembers = getAllMembers(false);
  return activeMembers.length > 0 ? activeMembers[0] : null;
}

/**
 * Check if member is VIP
 * @param {Object} member - Member object
 * @returns {boolean} Whether member is VIP
 */
export function isMemberVip(member) {
  return member && member.vip === 'Y';
}

/**
 * Get member's card type display
 * @param {Object} member - Member object
 * @returns {string} Card type with emoji
 */
export function getMemberCardTypeDisplay(member) {
  if (!member || !member.cardType) return '';

  const cardTypeEmojis = {
    'Standard': '🟢',
    'Gold': '🟡',
    'Platinum': '⚪',
    'Diamond': '💎'
  };

  const emoji = cardTypeEmojis[member.cardType] || '';
  return `${emoji} ${member.cardType}`;
}
