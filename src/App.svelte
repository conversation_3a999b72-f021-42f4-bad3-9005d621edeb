<script>
  import { onMount } from "svelte";
  import LandingPage from "./LandingPage.svelte";
  import PolicyList from "./PolicyList.svelte";
  import PolicyDetail from "./PolicyDetail.svelte";
  import ClaimList from "./ClaimList.svelte";
  import ClaimDetail from "./ClaimDetail.svelte";
  import MemberSelector from "./components/MemberSelector.svelte";
  import {
    selectedMemberStore,
    selectedMemberDisplayName,
    selectedMemberShortName,
    initializeMemberStore,
  } from "./stores/memberStore.js";

  // Application metadata
  const appInfo = {
    name: "Insurance Portal",
    version: "1.0.0",
    description:
      "Comprehensive insurance management platform built with Svelte and Tailwind CSS",
  };

  // Navigation state
  let currentPage = "landing";
  let selectedPolicyId = null;
  let selectedClaimId = null;

  // Navigation handler
  function handleNavigation(event) {
    currentPage = event.detail.page;
    selectedPolicyId = event.detail.policyId || null;
    selectedClaimId = event.detail.claimId || null;
    console.log(
      "Navigating to:",
      currentPage,
      selectedPolicyId ? `with policy ID: ${selectedPolicyId}` : "",
      selectedClaimId ? `with claim ID: ${selectedClaimId}` : "",
    );
  }

  // Page title mapping
  const pageTitles = {
    landing: "Insurance Portal - Home",
    "policy-list": "Policy List - Insurance Portal",
    "policy-detail": "Policy Detail - Insurance Portal",
    "claim-list": "Claim List - Insurance Portal",
    "claim-detail": "Claim Detail - Insurance Portal",
  };

  // Update document title when page changes
  $: if (typeof document !== "undefined") {
    document.title = pageTitles[currentPage] || "Insurance Portal";
  }

  // Initialize member store on app startup
  onMount(async () => {
    try {
      await initializeMemberStore();
      console.log("Member store initialized successfully");
    } catch (error) {
      console.error("Failed to initialize member store:", error);
    }
  });

  console.log("App initialized:", appInfo);
</script>

<div class="min-h-screen bg-gray-50">
  <!-- Navigation Header -->
  <nav class="bg-white shadow-sm border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <div class="flex items-center space-x-4">
          <button
            class="text-xl font-semibold text-gray-900 hover:text-blue-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md px-2 py-1"
            on:click={() => handleNavigation({ detail: { page: "landing" } })}
            aria-label="Go to home page"
          >
            🛡️ Insurance Portal
          </button>
          {#if currentPage !== "landing"}
            <span class="text-gray-300" aria-hidden="true">/</span>
            <span class="text-sm text-gray-600 capitalize">
              {currentPage.replace("-", " ")}
            </span>
          {/if}
        </div>
        <div class="flex items-center space-x-4">
          <!-- Member Selector -->
          <div class="hidden sm:block">
            <MemberSelector compact={true} />
          </div>

          <!-- Mobile Member Selector -->
          <div class="sm:hidden">
            <MemberSelector compact={true} showCardType={false} />
          </div>

          <!-- Welcome Message -->
          <div class="hidden md:flex items-center space-x-2">
            <span class="text-sm text-gray-500">Welcome back,</span>
            <span class="text-sm font-medium text-gray-700">
              {$selectedMemberShortName || "Guest"}
            </span>
          </div>

          <!-- User Avatar -->
          <div
            class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center"
            title={$selectedMemberDisplayName || "No member selected"}
          >
            <span class="text-white text-sm font-medium">
              {$selectedMemberStore
                ? $selectedMemberStore.nameEN
                  ? $selectedMemberStore.nameEN.charAt(0)
                  : $selectedMemberStore.nameTH
                    ? $selectedMemberStore.nameTH.charAt(0)
                    : $selectedMemberStore.memberCode.charAt(3)
                : "G"}
            </span>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <div class="flex-1">
    {#if currentPage === "landing"}
      <LandingPage on:navigate={handleNavigation} />
    {:else if currentPage === "policy-list"}
      <div class="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
        <!-- Back Navigation -->
        <div class="max-w-7xl mx-auto mb-8">
          <button
            class="inline-flex items-center px-4 py-2
                   bg-white hover:bg-gray-50
                   text-gray-700 font-medium rounded-md
                   border border-gray-300
                   transition-colors duration-200
                   focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            on:click={() => handleNavigation({ detail: { page: "landing" } })}
            aria-label="Go back to main navigation"
          >
            <svg
              class="mr-2 w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 19l-7-7 7-7"
              />
            </svg>
            Back to Home
          </button>
        </div>
        <div class="max-w-7xl mx-auto">
          <PolicyList on:navigate={handleNavigation} />
        </div>
      </div>
    {:else if currentPage === "policy-detail"}
      <PolicyDetail {selectedPolicyId} on:navigate={handleNavigation} />
    {:else if currentPage === "claim-list"}
      <div class="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
        <!-- Back Navigation -->
        <div class="max-w-7xl mx-auto mb-8">
          <button
            class="inline-flex items-center px-4 py-2
                   bg-white hover:bg-gray-50
                   text-gray-700 font-medium rounded-md
                   border border-gray-300
                   transition-colors duration-200
                   focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            on:click={() => handleNavigation({ detail: { page: "landing" } })}
            aria-label="Go back to main navigation"
          >
            <svg
              class="mr-2 w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 19l-7-7 7-7"
              />
            </svg>
            Back to Home
          </button>
        </div>
        <div class="max-w-7xl mx-auto">
          <ClaimList on:navigate={handleNavigation} />
        </div>
      </div>
    {:else if currentPage === "claim-detail"}
      <ClaimDetail {selectedClaimId} on:navigate={handleNavigation} />
    {:else}
      <!-- Fallback to landing page -->
      <LandingPage on:navigate={handleNavigation} />
    {/if}
  </div>

  <!-- Footer -->
  <footer class="bg-white border-t border-gray-200 mt-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="flex flex-col md:flex-row justify-between items-center">
        <div class="text-sm text-gray-500 mb-4 md:mb-0">
          © 2024 Insurance Portal. All rights reserved.
        </div>
        <div class="flex space-x-6 text-sm text-gray-500">
          <button
            class="hover:text-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded"
            aria-label="View privacy policy"
          >
            Privacy Policy
          </button>
          <button
            class="hover:text-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded"
            aria-label="View terms of service"
          >
            Terms of Service
          </button>
          <button
            class="hover:text-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded"
            aria-label="Contact support"
          >
            Support
          </button>
        </div>
      </div>
    </div>
  </footer>
</div>
