/**
 * dataStore.js
 * 
 * Centralized data management store for API data with caching and state management.
 * 
 * Features:
 * - Svelte stores for reactive data management
 * - In-memory caching with TTL (Time To Live)
 * - Loading and error state management
 * - Automatic cache invalidation
 * - Retry mechanisms for failed requests
 * 
 * Usage:
 * import { policiesStore, claimsStore, loadPolicies, loadClaims } from './stores/dataStore.js';
 */

import { writable, derived, get } from 'svelte/store';
import api from '../api/index.js';
import { selectedMemberStore } from './memberStore.js';

// Cache configuration
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds
const MAX_RETRY_ATTEMPTS = 3;

// Create cache storage
const cache = new Map();

// Helper function to create cache key with member context
function createCacheKey(type, params = {}, includeMemberContext = true) {
  let keyParts = [type];

  // Add member context if requested and available
  if (includeMemberContext) {
    const currentMember = get(selectedMemberStore);
    if (currentMember && currentMember.memberCode) {
      keyParts.push(`member:${currentMember.memberCode}`);
    }
  }

  // Add other parameters
  const paramString = Object.keys(params)
    .sort()
    .map(key => `${key}:${params[key]}`)
    .join('|');

  if (paramString) {
    keyParts.push(paramString);
  }

  return keyParts.join('_');
}

// Helper function to check if cache entry is valid
function isCacheValid(entry) {
  return entry && (Date.now() - entry.timestamp) < CACHE_TTL;
}

// Helper function to get from cache
function getFromCache(key) {
  const entry = cache.get(key);
  if (isCacheValid(entry)) {
    return entry.data;
  }
  cache.delete(key);
  return null;
}

// Helper function to set cache
function setCache(key, data) {
  cache.set(key, {
    data,
    timestamp: Date.now()
  });
}

// Create base store structure
function createDataStore(initialState = {}) {
  const { subscribe, set, update } = writable({
    data: null,
    loading: false,
    error: null,
    lastUpdated: null,
    ...initialState
  });

  return {
    subscribe,
    set,
    update,
    setLoading: (loading) => update(state => ({ ...state, loading, error: loading ? null : state.error })),
    setData: (data) => update(state => ({ ...state, data, loading: false, error: null, lastUpdated: Date.now() })),
    setError: (error) => update(state => ({ ...state, error, loading: false })),
    reset: () => set({ data: null, loading: false, error: null, lastUpdated: null })
  };
}

// Policies store
export const policiesStore = createDataStore();

// Claims store  
export const claimsStore = createDataStore();

// Policy detail store
export const policyDetailStore = createDataStore();

// Claim detail store
export const claimDetailStore = createDataStore();

// Derived stores for computed values
export const activePolicies = derived(
  policiesStore,
  $policies => $policies.data?.filter(policy => policy.status === 'Active') || []
);

export const pendingClaims = derived(
  claimsStore,
  $claims => $claims.data?.filter(claim => claim.status === 'Processing' || claim.status === 'Under Review') || []
);

// Load policies function with member context
export async function loadPolicies(params = {}, forceRefresh = false) {
  const currentMember = get(selectedMemberStore);
  const cacheKey = createCacheKey('policies', params, true);

  // Check cache first (unless force refresh)
  if (!forceRefresh) {
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      policiesStore.setData(cachedData);
      return cachedData;
    }
  }

  policiesStore.setLoading(true);

  try {
    let result;

    // Use member context if available and no specific params provided
    if (currentMember && Object.keys(params).length === 0) {
      // Use member's citizen ID for policy search
      result = await api.policies.searchByCitizenId(currentMember.citizenID);
    } else if (params.citizenId) {
      result = await api.policies.searchByCitizenId(params.citizenId);
    } else if (params.policyNumber && params.name) {
      result = await api.policies.searchByPolicyAndName(params.policyNumber, params.name);
    } else if (params.name) {
      result = await api.policies.searchByName(params.name);
    } else if (currentMember) {
      // Fallback to current member's citizen ID
      result = await api.policies.searchByCitizenId(currentMember.citizenID);
    } else {
      // Default search for demo
      result = await api.policies.searchByCitizenId('1234567890123');
    }

    if (result.success && result.data) {
      const policies = result.data;
      setCache(cacheKey, policies);
      policiesStore.setData(policies);
      return policies;
    } else {
      throw new Error(result.message || 'Failed to load policies');
    }
  } catch (error) {
    console.error('Error loading policies:', error);
    policiesStore.setError(error);
    throw error;
  }
}

// Load policy detail function
export async function loadPolicyDetail(memberCode, forceRefresh = false) {
  if (!memberCode) {
    const error = new Error('Member code is required');
    policyDetailStore.setError(error);
    throw error;
  }

  const cacheKey = createCacheKey('policy_detail', { memberCode });

  // Check cache first (unless force refresh)
  if (!forceRefresh) {
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      policyDetailStore.setData(cachedData);
      return cachedData;
    }
  }

  policyDetailStore.setLoading(true);

  try {
    const result = await api.members.getPolicyDetail(memberCode);

    if (result.success && result.data) {
      const policyDetail = result.data;
      setCache(cacheKey, policyDetail);
      policyDetailStore.setData(policyDetail);
      return policyDetail;
    } else {
      throw new Error(result.message || 'Failed to load policy details');
    }
  } catch (error) {
    console.error('Error loading policy detail:', error);
    policyDetailStore.setError(error);
    throw error;
  }
}

// Load claims function with member context
export async function loadClaims(params = {}, forceRefresh = false) {
  const currentMember = get(selectedMemberStore);
  const cacheKey = createCacheKey('claims', params, true);

  // Check cache first (unless force refresh)
  if (!forceRefresh) {
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      claimsStore.setData(cachedData);
      return cachedData;
    }
  }

  claimsStore.setLoading(true);

  try {
    let result;

    // Use member context if available and no specific params provided
    if (currentMember && Object.keys(params).length === 0) {
      // Use current member's member code for claims search
      result = await api.claims.getByMemberCode(currentMember.memberCode);
    } else if (params.memberCode) {
      result = await api.claims.getByMemberCode(params.memberCode);
    } else if (params.citizenId) {
      result = await api.claims.getByCitizenId(params.citizenId);
    } else if (currentMember) {
      // Fallback to current member's member code
      result = await api.claims.getByMemberCode(currentMember.memberCode);
    } else {
      // Default search for demo
      result = await api.claims.getByMemberCode('MEM001');
    }

    if (result.success && result.data) {
      const claims = result.data;
      setCache(cacheKey, claims);
      claimsStore.setData(claims);
      return claims;
    } else {
      throw new Error(result.message || 'Failed to load claims');
    }
  } catch (error) {
    console.error('Error loading claims:', error);
    claimsStore.setError(error);
    throw error;
  }
}

// Load claim detail function
export async function loadClaimDetail(claimId, forceRefresh = false) {
  if (!claimId) {
    const error = new Error('Claim ID is required');
    claimDetailStore.setError(error);
    throw error;
  }

  const cacheKey = createCacheKey('claim_detail', { claimId });

  // Check cache first (unless force refresh)
  if (!forceRefresh) {
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      claimDetailStore.setData(cachedData);
      return cachedData;
    }
  }

  claimDetailStore.setLoading(true);

  try {
    // Note: This would need to be implemented in the API client
    // For now, we'll simulate with a generic API call
    const result = await api.system.health(); // Placeholder

    if (result.success) {
      // This would be the actual claim detail data
      const claimDetail = { id: claimId, status: 'Processing' }; // Placeholder
      setCache(cacheKey, claimDetail);
      claimDetailStore.setData(claimDetail);
      return claimDetail;
    } else {
      throw new Error('Failed to load claim details');
    }
  } catch (error) {
    console.error('Error loading claim detail:', error);
    claimDetailStore.setError(error);
    throw error;
  }
}

// Utility functions
export function clearCache() {
  cache.clear();
}

export function clearCacheByType(type) {
  for (const [key] of cache) {
    if (key.startsWith(type)) {
      cache.delete(key);
    }
  }
}

export function refreshAllData() {
  clearCache();
  // Reset all stores
  policiesStore.reset();
  claimsStore.reset();
  policyDetailStore.reset();
  claimDetailStore.reset();
}

// Retry function with exponential backoff
export async function retryOperation(operation, maxAttempts = MAX_RETRY_ATTEMPTS) {
  let lastError;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;

      if (attempt === maxAttempts) {
        break;
      }

      // Exponential backoff: 1s, 2s, 4s, etc.
      const delay = Math.pow(2, attempt - 1) * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
}
